// 验证文件夹 API 修复的简单脚本
// 在 Electron 渲染进程的开发者控制台中运行

function verifyFolderAPIFix() {
  console.log('🔍 验证文件夹 API 修复...');
  
  // 1. 检查基础 API 可用性
  console.log('\n📋 基础检查:');
  console.log('✓ window.electronAPI 存在:', !!window.electronAPI);
  console.log('✓ getFolderFiles 方法存在:', !!window.electronAPI?.getFolderFiles);
  console.log('✓ getFileInfo 方法存在:', !!window.electronAPI?.getFileInfo);
  console.log('✓ showOpenDialog 方法存在:', !!window.electronAPI?.showOpenDialog);
  
  // 2. 检查方法类型
  console.log('\n🔧 方法类型检查:');
  if (window.electronAPI?.getFolderFiles) {
    console.log('✓ getFolderFiles 类型:', typeof window.electronAPI.getFolderFiles);
  } else {
    console.log('❌ getFolderFiles 不存在');
  }
  
  if (window.electronAPI?.getFileInfo) {
    console.log('✓ getFileInfo 类型:', typeof window.electronAPI.getFileInfo);
  } else {
    console.log('❌ getFileInfo 不存在');
  }
  
  // 3. 显示所有可用的 API
  console.log('\n📋 所有可用的 API:');
  if (window.electronAPI) {
    const apis = Object.keys(window.electronAPI);
    apis.forEach(api => {
      console.log(`  - ${api}: ${typeof window.electronAPI[api]}`);
    });
  }
  
  // 4. 修复状态总结
  console.log('\n📊 修复状态总结:');
  const hasGetFolderFiles = !!window.electronAPI?.getFolderFiles;
  const hasGetFileInfo = !!window.electronAPI?.getFileInfo;
  const hasShowOpenDialog = !!window.electronAPI?.showOpenDialog;
  
  if (hasGetFolderFiles && hasGetFileInfo && hasShowOpenDialog) {
    console.log('✅ 所有必需的 API 都可用，修复成功！');
    console.log('💡 现在可以测试文件夹选择功能了');
    console.log('💡 运行 quickTest() 来测试具体功能');
  } else {
    console.log('❌ 部分 API 仍然不可用，需要进一步检查');
    console.log('💡 可能需要重启 Electron 应用');
    
    if (!hasGetFolderFiles) console.log('   - getFolderFiles 缺失');
    if (!hasGetFileInfo) console.log('   - getFileInfo 缺失');
    if (!hasShowOpenDialog) console.log('   - showOpenDialog 缺失');
  }
  
  return {
    hasGetFolderFiles,
    hasGetFileInfo,
    hasShowOpenDialog,
    allAPIsAvailable: hasGetFolderFiles && hasGetFileInfo && hasShowOpenDialog
  };
}

// 运行验证
const result = verifyFolderAPIFix();

// 如果所有 API 都可用，提供下一步指导
if (result.allAPIsAvailable) {
  console.log('\n🎯 下一步测试建议:');
  console.log('1. 在应用界面中点击"选择文件夹"按钮');
  console.log('2. 选择一个包含文件的文件夹');
  console.log('3. 检查文件是否正确添加到上传列表');
  console.log('4. 或者运行 quickTest() 进行自动化测试');
} else {
  console.log('\n🔧 故障排除建议:');
  console.log('1. 确保 Electron 应用已完全重启');
  console.log('2. 检查控制台是否有其他错误信息');
  console.log('3. 确认 preload 脚本已正确编译');
  console.log('4. 检查 main.ts 中的 IPC 处理器是否正确注册');
}
