// 测试文件上传行为的脚本
// 这个脚本可以在 Electron 渲染进程的开发者控制台中运行

async function testUploadBehavior() {
  console.log('🧪 开始测试文件上传行为...');
  
  // 检查 API 是否可用
  if (!window.electronAPI) {
    console.error('❌ Electron API 不可用');
    return;
  }

  console.log('📋 测试项目：');
  console.log('1. 文件选择器应该只选择文件，不启动上传');
  console.log('2. 文件夹选择器应该只选择文件夹，不启动上传');
  console.log('3. 拖拽文件应该只添加到列表，不启动上传');
  console.log('4. 只有点击"开始上传"才应该启动实际上传');

  // 测试通用文件选择 API
  console.log('\n📁 测试通用文件选择 API...');
  try {
    const fileResult = await window.electronAPI.showOpenDialog({
      properties: ['openFile', 'multiSelections'],
      title: '测试文件选择',
      filters: [
        { name: '所有文件', extensions: ['*'] },
        { name: '文本文件', extensions: ['txt', 'md'] },
      ],
    });
    
    if (!fileResult.canceled && fileResult.filePaths) {
      console.log('✅ 文件选择成功:', fileResult.filePaths);
      console.log('📝 注意：这里应该只是选择了文件，没有启动上传');
    } else {
      console.log('ℹ️ 用户取消了文件选择');
    }
  } catch (error) {
    console.error('❌ 文件选择测试失败:', error);
  }

  // 测试通用文件夹选择 API
  console.log('\n📂 测试通用文件夹选择 API...');
  try {
    const folderResult = await window.electronAPI.showOpenDialog({
      properties: ['openDirectory'],
      title: '测试文件夹选择',
    });
    
    if (!folderResult.canceled && folderResult.filePaths) {
      console.log('✅ 文件夹选择成功:', folderResult.filePaths[0]);
      console.log('📝 注意：这里应该只是选择了文件夹，没有启动上传');
      
      // 测试获取文件夹内容
      try {
        const folderFiles = await window.electronAPI.getFolderFiles(folderResult.filePaths[0]);
        console.log(`📄 文件夹包含 ${folderFiles.length} 个文件`);
        if (folderFiles.length > 0) {
          console.log('📋 前3个文件:', folderFiles.slice(0, 3).map(f => f.relativePath));
        }
      } catch (error) {
        console.error('❌ 获取文件夹内容失败:', error);
      }
    } else {
      console.log('ℹ️ 用户取消了文件夹选择');
    }
  } catch (error) {
    console.error('❌ 文件夹选择测试失败:', error);
  }

  // 测试文件信息获取
  console.log('\n📊 测试文件信息获取 API...');
  try {
    // 尝试获取一个常见文件的信息（如果存在）
    const testPaths = [
      '/Users/<USER>/Desktop',
      '/Users/<USER>/Documents',
      'C:\\Users\\<USER>\\Desktop',
      'C:\\Users\\<USER>\\Documents'
    ];
    
    for (const testPath of testPaths) {
      try {
        const fileInfo = await window.electronAPI.getFileInfo(testPath);
        console.log(`✅ 文件信息获取成功: ${testPath}`);
        console.log('📋 信息:', {
          isFile: fileInfo.isFile,
          isDirectory: fileInfo.isDirectory,
          size: fileInfo.size,
          mtime: new Date(fileInfo.mtime).toLocaleString()
        });
        break; // 找到一个可用路径就停止
      } catch (error) {
        // 继续尝试下一个路径
        continue;
      }
    }
  } catch (error) {
    console.error('❌ 文件信息获取测试失败:', error);
  }

  console.log('\n✅ 文件上传行为测试完成');
  console.log('📝 请在实际界面中验证：');
  console.log('   1. 点击"选择文件"按钮，确认只添加文件到列表');
  console.log('   2. 点击"选择文件夹"按钮，确认只添加文件夹内容到列表');
  console.log('   3. 拖拽文件到上传区域，确认只添加到列表');
  console.log('   4. 点击"开始上传"按钮，确认开始实际上传');
}

// 运行测试
testUploadBehavior();
