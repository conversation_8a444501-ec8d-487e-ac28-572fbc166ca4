// Electron API 类型定义

export interface TusUploadConfig {
  endpoint: string;
  chunkSize?: number;
  retryDelays?: number[];
  parallelUploads?: number;
  metadata?: Record<string, string>;
  headers?: Record<string, string>;
}

export interface UploadTask {
  id: string;
  filePath: string;
  fileName: string;
  fileSize: number;
  uploadUrl?: string;
  progress: number;
  status: "pending" | "uploading" | "paused" | "completed" | "error" | "cancelled";
  bytesUploaded: number;
  uploadSpeed: number;
  remainingTime: number;
  startTime: Date;
  error?: string;
  metadata?: Record<string, string>;
  resumable: boolean;
  batchId?: string;
  isSubTask?: boolean;
}

export interface ApiResponse<T = any> {
  success: boolean;
  error?: string;
  taskId?: string;
  task?: UploadTask;
  tasks?: UploadTask[];
  data?: T;
}

// 批量上传任务类型
export interface BatchUploadTask {
  id: string;
  type: "batch";
  batchName: string;
  folderPath?: string;
  totalFiles: number;
  completedFiles: number;
  failedFiles: number;
  pausedFiles: number;
  totalSize: number;
  uploadedSize: number;
  progress: number;
  status: "pending" | "uploading" | "paused" | "completed" | "error" | "cancelled";
  startTime: Date;
  endTime?: Date;
  error?: string;
  subTasks: string[];
  expanded?: boolean;
  metadata?: Record<string, string>;
  avgUploadSpeed: number;
  estimatedRemainingTime: number;
}

// 下载相关类型
export interface DownloadTask {
  id: string;
  url: string;
  fileName: string;
  filePath: string;
  fileSize?: number;
  progress: number;
  status: "pending" | "downloading" | "paused" | "completed" | "error" | "cancelled";
  bytesDownloaded: number;
  downloadSpeed: number;
  remainingTime: number;
  startTime: Date;
  endTime?: Date;
  error?: string;
  metadata?: Record<string, any>;
  resumable: boolean;
  chunkSize: number;
  totalChunks: number;
  completedChunks: number;
  headers?: Record<string, string>;
}

export interface StreamDownloadConfig {
  chunkSize?: number;
  maxConcurrent?: number;
  retryDelays?: number[];
  timeout?: number;
  headers?: Record<string, string>;
  downloadDir?: string;
}

export interface DownloadApiResponse<T = any> {
  success: boolean;
  error?: string;
  taskId?: string;
  task?: DownloadTask;
  tasks?: DownloadTask[];
  data?: T;
}

declare global {
  interface Window {
    electronAPI: {
      // 基础功能
      getAppVersion: () => Promise<string>;
      getPlatform: () => Promise<string>;
      showOpenDialog: (options: any) => Promise<any>;
      showSaveDialog: (options: any) => Promise<any>;
      showMessageBox: (options: any) => Promise<any>;

      // 文件系统功能
      getFileInfo: (filePath: string) => Promise<{
        size: number;
        mtime: number;
        isFile: boolean;
        isDirectory: boolean;
      }>;
      getFolderFiles: (folderPath: string) => Promise<
        Array<{
          path: string;
          relativePath: string;
          name: string;
          size: number;
        }>
      >;

      // TUS 上传功能（整合了 API 调用和事件监听器）
      tus: {
        // API 调用方法
        createUpload: (filePath: string, metadata?: Record<string, string>) => Promise<ApiResponse>;
        createUploadFromDialog: () => Promise<ApiResponse>;
        startUpload: (taskId: string) => Promise<ApiResponse>;
        pauseUpload: (taskId: string) => Promise<ApiResponse>;
        resumeUpload: (taskId: string) => Promise<ApiResponse>;
        cancelUpload: (taskId: string) => Promise<ApiResponse>;
        retryUpload: (taskId: string) => Promise<ApiResponse>;
        deleteTask: (taskId: string) => Promise<ApiResponse>;
        getAllTasks: () => Promise<ApiResponse>;
        getTask: (taskId: string) => Promise<ApiResponse>;
        getActiveTasks: () => Promise<ApiResponse>;
        getTaskUploadUrl: (taskId: string) => Promise<ApiResponse>;
        updateConfig: (config: Partial<TusUploadConfig>) => Promise<ApiResponse>;
        clearCompletedTasks: () => Promise<ApiResponse>;

        // 事件监听器方法
        onUploadTaskCreated: (callback: (taskId: string, task: UploadTask) => void) => void;
        onUploadTaskProgress: (callback: (taskId: string, progress: number, bytesUploaded: number, bytesTotal: number) => void) => void;
        onUploadTaskStatusChanged: (callback: (taskId: string, status: string, error?: string) => void) => void;
        onUploadTaskCompleted: (callback: (taskId: string) => void) => void;
        onUploadTaskError: (callback: (taskId: string, error: string) => void) => void;
        removeAllListeners: (channel: string) => void;
      };

      // StreamSaver 下载功能（整合了 API 调用和事件监听器）
      download: {
        // API 调用方法
        createTask: (fileName?: string, savePath?: string, metadata?: Record<string, string>) => Promise<DownloadApiResponse>;
        createTasks: (downloads: Array<{ fileName?: string; savePath?: string; metadata?: Record<string, string> }>) => Promise<DownloadApiResponse>;
        createTaskWithDialog: (defaultFileName?: string, metadata?: Record<string, string>) => Promise<DownloadApiResponse>;
        startDownload: (taskId: string) => Promise<DownloadApiResponse>;
        pauseDownload: (taskId: string) => Promise<DownloadApiResponse>;
        resumeDownload: (taskId: string) => Promise<DownloadApiResponse>;
        cancelDownload: (taskId: string) => Promise<DownloadApiResponse>;
        retryDownload: (taskId: string) => Promise<DownloadApiResponse>;
        deleteTask: (taskId: string) => Promise<DownloadApiResponse>;
        getAllTasks: () => Promise<DownloadApiResponse>;
        getTask: (taskId: string) => Promise<DownloadApiResponse>;
        getActiveTasks: () => Promise<DownloadApiResponse>;
        updateConfig: (config: Partial<StreamDownloadConfig>) => Promise<DownloadApiResponse>;
        clearCompletedTasks: () => Promise<DownloadApiResponse>;
        clearAllTasks: () => Promise<DownloadApiResponse>;
        startBatch: (taskIds: string[]) => Promise<DownloadApiResponse>;
        pauseBatch: (taskIds: string[]) => Promise<DownloadApiResponse>;
        resumeBatch: (taskIds: string[]) => Promise<DownloadApiResponse>;
        getStats: () => Promise<DownloadApiResponse>;

        // 事件监听器方法
        onDownloadTaskCreated: (callback: (taskId: string, task: DownloadTask) => void) => void;
        onDownloadTaskProgress: (callback: (taskId: string, progress: number, bytesDownloaded: number, bytesTotal: number) => void) => void;
        onDownloadTaskStatusChanged: (callback: (taskId: string, status: string, error?: string) => void) => void;
        onDownloadTaskCompleted: (callback: (taskId: string) => void) => void;
        onDownloadTaskError: (callback: (taskId: string, error: string) => void) => void;
        removeAllListeners: (channel: string) => void;
      };

      // 主进程消息监听器
      onMainProcessMessage: (callback: (data: string) => void) => void;

      // 认证相关 API
      onAuthToken: (callback: (token: string) => void) => void;
      clearAuthState: () => void;
    };
  }
}
