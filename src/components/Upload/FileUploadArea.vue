<template>
  <div class="w-full h-full min-h-0">
    <!-- 拖拽上传区域 -->
    <div
      class="overflow-hidden relative rounded-xl border-2 border-dashed transition-all duration-200 border-border bg-card"
      :class="{
        'border-primary bg-accent': isDragging,
        'border-solid hover:border-primary hover:bg-accent': files.length > 0,
        'hover:border-primary hover:bg-accent': files.length === 0
      }" @dragover="handleDragOver" @dragleave="handleDragLeave" @drop="handleDropFiles">
      <div class="flex flex-col gap-2 items-center p-4 text-center sm:p-6">
        <div class="flex justify-center items-center w-12 h-12 rounded-full sm:w-16 sm:h-16 bg-muted">
          <Upload class="w-8 h-8 sm:w-12 sm:h-12 text-muted-foreground" />
        </div>

        <div class="max-w-96">
          <h3 class="mb-2 text-base font-semibold sm:text-lg text-foreground">拖拽文件到此处上传</h3>
        </div>

        <div class="flex flex-wrap gap-2 justify-center items-center">
          <Button @click="selectFiles" :disabled="!canAddMore">
            <Plus class="mr-2 w-4 h-4" />
            选择文件
          </Button>

          <Button @click="selectDirectories" :disabled="!canAddMore" variant="outline">
            <FolderOpen class="mr-2 w-4 h-4" />
            选择文件夹
          </Button>

          <!-- 隐藏的文件输入 -->
          <input ref="fileInputRef" type="file" :accept="accept" :multiple="multiple" class="hidden"
            @change="handleFileInputChange" />

          <!-- 隐藏的目录输入 -->
          <input ref="dirInputRef" type="file" webkitdirectory class="hidden" @change="handleFileInputChange" />
        </div>

        <div v-if="!canAddMore" class="text-sm font-medium text-destructive">
          已达到最大文件数量限制 ({{ maxFiles }})
        </div>
      </div>

      <!-- 拖拽遮罩 -->
      <div v-if="isDragging"
        class="flex absolute inset-0 z-10 justify-center items-center backdrop-blur-sm bg-primary/10">
        <div class="flex flex-col gap-2 items-center font-semibold text-primary">
          <Download class="w-8 h-8" />
          <span class="text-lg">释放以上传文件</span>
        </div>
      </div>
    </div>

    <!-- 文件列表 -->
    <div v-if="files.length > 0" class="flex flex-col flex-1 mt-6 min-h-0">
      <div class="flex flex-col gap-2 pb-2 mb-4 border-b sm:items-center sm:justify-between sm:gap-4 border-border">
        <h4 class="text-base font-semibold text-foreground">
          已选择 {{ files.length }} 个文件
          <span v-if="folderGroups.length > 0" class="text-sm font-normal text-muted-foreground">
            (来自 {{ folderGroups.length }} 个文件夹)
          </span>
        </h4>
        <div class="flex flex-wrap gap-2 items-center sm:gap-4">
          <span class="text-sm text-muted-foreground">总大小: {{ formatFileSize(totalSize) }}</span>
          <Button @click="toggleViewMode" variant="ghost" size="sm">
            <component :is="viewMode === 'grid' ? List : LayoutGrid" class="mr-1 w-4 h-4" />
            {{ viewMode === 'grid' ? '列表视图' : '网格视图' }}
          </Button>
          <Button variant="ghost" size="sm" @click="handleClearFiles">
            <Trash2 class="mr-1 w-4 h-4" />
            清空
          </Button>
        </div>
      </div>

      <div class="overflow-y-auto flex-1 rounded-lg border border-border bg-muted/30">
        <!-- 统一的文件和文件夹显示列表 -->
        <div class="grid p-3" :class="{
          'grid-cols-[repeat(auto-fill,minmax(80px,1fr))] sm:grid-cols-[repeat(auto-fill,minmax(90px,1fr))] lg:grid-cols-[repeat(auto-fill,minmax(100px,1fr))] gap-2': viewMode === 'grid',
          'grid-cols-1 gap-2': viewMode === 'list'
        }">
          <template v-for="item in displayItems" :key="item.id">
            <!-- 文件夹项 -->
            <FolderItem v-if="item.type === 'folder'" :folder="item" :view-mode="viewMode" @remove="removeFolderGroup"
              @toggle-expand="toggleFolderExpand" @file-remove="handleFileRemove" />

            <!-- 文件项 -->
            <FileItem v-else :file="item" :view-mode="viewMode" @remove="handleFileRemove" />
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { toast } from 'vue-sonner'
import {
  Upload,
  Plus,
  Download,
  Trash2,
  FolderOpen,
  LayoutGrid,
  List
} from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import FileItem from './FileItem.vue'
import FolderItem, { type FolderGroup } from './FolderItem.vue'
import { useFileUpload, type UseFileUploadOptions } from './composables/useFileUpload'
import { detectUploadStrategy, type UploadStrategy } from '@/lib/upload-utils'

// Props
const props = withDefaults(defineProps<UseFileUploadOptions & {
  modelValue?: File[]
}>(), {
  accept: '',
  multiple: true,
  maxSize: 100 * 1024 * 1024, // 100MB
  maxFiles: 10,
  allowDirectories: true
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [files: File[]]
  'files-change': [files: File[]]
  'error': [errors: string[]]
  'upload-strategy': [uploadStrategy: UploadStrategy]
}>()

// 文件上传逻辑
const {
  files,
  isDragging,
  totalSize,
  canAddMore,
  handleFileInput,
  processFiles,
  handleDragOver,
  handleDragLeave,
  handleDrop,
  removeFile,
  clearFiles,
  formatFileSize,
  accept,
  multiple,
  maxFiles,
  allowDirectories
} = useFileUpload(props)

// 文件输入引用
const fileInputRef = ref<HTMLInputElement>()
const dirInputRef = ref<HTMLInputElement>()

// 视图模式
const viewMode = ref<'grid' | 'list'>('grid')
const expandedFolders = ref<Set<string>>(new Set())

// 计算文件夹分组
const folderGroups = computed<FolderGroup[]>(() => {
  const groups = new Map<string, FolderGroup>()

  files.value.forEach(file => {
    // 通过文件的webkitRelativePath获取文件夹路径
    const relativePath = (file.file as any).webkitRelativePath || ''

    if (relativePath) {
      const pathParts = relativePath.split('/')
      const folderPath = pathParts.slice(0, -1).join('/')
      const folderName = pathParts[0] || 'Root'

      if (!groups.has(folderPath)) {
        groups.set(folderPath, {
          path: folderPath,
          name: folderName,
          files: [],
          totalSize: 0,
          expanded: expandedFolders.value.has(folderPath)
        })
      }

      const group = groups.get(folderPath)!
      group.files.push(file)
      group.totalSize += file.size
    }
  })

  return Array.from(groups.values()).sort((a, b) => a.name.localeCompare(b.name))
})

// 单独文件（不属于任何文件夹）
const singleFiles = computed(() => {
  return files.value.filter(file => {
    const relativePath = (file.file as any).webkitRelativePath || ''
    return !relativePath
  })
})

// 混合显示项（文件夹 + 单独文件）
const displayItems = computed(() => {
  const items: Array<any> = []

  // 添加文件夹项
  folderGroups.value.forEach(group => {
    items.push({
      id: `folder-${group.path}`,
      type: 'folder',
      path: group.path,
      name: group.name,
      files: group.files,
      totalSize: group.totalSize,
      expanded: group.expanded
    })
  })

  // 添加单独文件项
  singleFiles.value.forEach(file => {
    items.push({
      ...file,
      type: 'file'
    })
  })

  return items
})

// 切换视图模式
const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'grid' ? 'list' : 'grid'
}

// 切换文件夹展开状态
const toggleFolderExpand = (folderPath: string) => {
  if (expandedFolders.value.has(folderPath)) {
    expandedFolders.value.delete(folderPath)
  } else {
    expandedFolders.value.add(folderPath)
  }
}

// 移除整个文件夹组
const removeFolderGroup = (folderPath: string) => {
  const group = folderGroups.value.find(g => g.path === folderPath)
  if (group) {
    // 移除文件夹中的所有文件
    group.files.forEach(file => removeFile(file.id))
    expandedFolders.value.delete(folderPath)
    updateParentComponent()

    // 重新计算上传策略
    const allFiles = files.value.map(f => f.file)
    if (allFiles.length > 0) {
      const uploadStrategy = detectUploadStrategy(allFiles)
      emit('upload-strategy', uploadStrategy)
    } else {
      // 如果没有文件了，重置策略
      emit('upload-strategy', { type: 'single', folders: [], singleFiles: [], description: '无文件' })
    }
  }
}

// 选择文件 - 只选择文件，不启动上传
const selectFiles = async () => {
  // 检查是否在 Electron 环境中
  const electronAPI = (window as any).electronAPI;
  if (electronAPI?.showOpenDialog) {
    try {
      // 使用 Electron 通用文件选择器，只选择文件不启动上传
      const result = await electronAPI.showOpenDialog({
        properties: ['openFile', 'multiSelections'],
        title: '选择要上传的文件',
        filters: [
          { name: '所有文件', extensions: ['*'] },
          { name: '图片', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'] },
          { name: '视频', extensions: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'] },
          { name: '文档', extensions: ['pdf', 'doc', 'docx', 'txt', 'rtf'] },
        ],
      });

      if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
        // 将选择的文件路径转换为 File 对象并添加到列表
        await addFilesFromPaths(result.filePaths);
        console.log(`通过原生选择器选择了 ${result.filePaths.length} 个文件`);
        toast.success(`已选择 ${result.filePaths.length} 个文件，请点击开始上传`);
        return;
      }
    } catch (error) {
      console.error('原生文件选择出错:', error);
      toast.warning('原生文件选择出错，使用浏览器文件选择器');
      // 降级到浏览器文件选择器
      fileInputRef.value?.click();
    }
  } else {
    // 非 Electron 环境或 API 不可用，使用浏览器文件选择器
    fileInputRef.value?.click();
  }
}

// 选择目录 - 只选择文件夹，不启动上传
const selectDirectories = async () => {
  if (!allowDirectories) return;

  // 检查是否在 Electron 环境中
  const electronAPI = (window as any).electronAPI;
  if (electronAPI?.showOpenDialog) {
    try {
      // 使用 Electron 通用文件夹选择器，只选择文件夹不启动上传
      const result = await electronAPI.showOpenDialog({
        properties: ['openDirectory'],
        title: '选择要上传的文件夹',
      });

      if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
        // 将选择的文件夹路径转换为文件列表并添加到列表
        await addFolderFromPath(result.filePaths[0]);
        console.log(`通过原生选择器选择了文件夹: ${result.filePaths[0]}`);
        return;
      }
    } catch (error) {
      console.error('原生文件夹选择出错:', error);
      toast.warning('原生文件夹选择出错，使用浏览器文件夹选择器');
      // 降级到浏览器文件夹选择器
      dirInputRef.value?.click();
    }
  } else {
    // 非 Electron 环境或 API 不可用，使用浏览器文件夹选择器
    dirInputRef.value?.click();
  }
}

// 从文件路径添加文件到列表
const addFilesFromPaths = async (filePaths: string[]) => {
  const electronAPI = (window as any).electronAPI;
  if (!electronAPI?.getFileInfo) {
    console.error('Electron API 不可用');
    return;
  }

  for (const filePath of filePaths) {
    try {
      const fileInfo = await electronAPI.getFileInfo(filePath);
      if (fileInfo.isFile) {
        // 创建一个模拟的 File 对象，包含路径信息
        const fileName = filePath.split('/').pop() || filePath.split('\\').pop() || 'unknown';
        const mockFile = new File([], fileName, {
          type: '',
          lastModified: fileInfo.mtime
        });

        // 添加路径信息到 File 对象
        (mockFile as any).path = filePath;
        (mockFile as any).size = fileInfo.size;

        // 使用现有的文件处理逻辑
        const processedFiles = await processFiles([mockFile]);
        handleErrors(processedFiles);
      }
    } catch (error) {
      console.error(`处理文件失败: ${filePath}`, error);
    }
  }

  // 更新父组件后重新计算整体上传策略
  updateParentComponent();

  // 基于所有文件重新计算上传策略
  const allFiles = files.value.map(f => f.file);
  const uploadStrategy = detectUploadStrategy(allFiles);

  // 将上传策略传递给父组件
  emit('upload-strategy', uploadStrategy);
};

// 从文件夹路径添加文件到列表
const addFolderFromPath = async (folderPath: string) => {
  const electronAPI = (window as any).electronAPI;
  if (!electronAPI?.getFolderFiles) {
    console.error('Electron API 不可用');
    return;
  }

  try {
    const folderFiles = await electronAPI.getFolderFiles(folderPath);

    for (const fileInfo of folderFiles) {
      try {
        // 创建一个模拟的 File 对象，包含路径和相对路径信息
        const mockFile = new File([], fileInfo.name, {
          type: '',
          lastModified: Date.now()
        });

        // 添加路径信息到 File 对象
        (mockFile as any).path = fileInfo.path;
        (mockFile as any).size = fileInfo.size;
        (mockFile as any).webkitRelativePath = fileInfo.relativePath;

        // 使用现有的文件处理逻辑
        const processedFiles = await processFiles([mockFile]);
        handleErrors(processedFiles);
      } catch (error) {
        console.error(`处理文件失败: ${fileInfo.path}`, error);
      }
    }

    toast.success(`已选择文件夹，共 ${folderFiles.length} 个文件，请点击开始上传`);

    // 更新父组件后重新计算整体上传策略
    updateParentComponent();

    // 基于所有文件重新计算上传策略
    const allFiles = files.value.map(f => f.file);
    const uploadStrategy = detectUploadStrategy(allFiles);

    // 将上传策略传递给父组件
    emit('upload-strategy', uploadStrategy);
  } catch (error) {
    console.error('获取文件夹内容失败:', error);
    toast.error('获取文件夹内容失败');
  }
};

// 处理文件输入变化
const handleFileInputChange = async (event: Event) => {
  const input = event.target as HTMLInputElement
  if (!input.files || input.files.length === 0) {
    return
  }

  const inputErrors = await handleFileInput(event)
  handleErrors(inputErrors)

  // 更新父组件后重新计算整体上传策略
  updateParentComponent()

  // 基于所有文件重新计算上传策略
  const allFiles = files.value.map(f => f.file)
  const uploadStrategy = detectUploadStrategy(allFiles)

  // 将上传策略传递给父组件
  emit('upload-strategy', uploadStrategy)
}

// 处理拖拽文件
const handleDropFiles = async (event: DragEvent) => {
  const { errors: dropErrors } = await handleDrop(event)
  handleErrors(dropErrors)

  // 更新父组件后重新计算整体上传策略
  updateParentComponent()

  // 基于所有文件重新计算上传策略
  const allFiles = files.value.map(f => f.file)
  const uploadStrategy = detectUploadStrategy(allFiles)

  // 将上传策略传递给父组件
  emit('upload-strategy', uploadStrategy)
}

// 处理文件移除
const handleFileRemove = (fileId: string) => {
  removeFile(fileId)
  updateParentComponent()

  // 重新计算上传策略
  const allFiles = files.value.map(f => f.file)
  if (allFiles.length > 0) {
    const uploadStrategy = detectUploadStrategy(allFiles)
    emit('upload-strategy', uploadStrategy)
  }
}

// 处理清空文件
const handleClearFiles = () => {
  clearFiles()
  expandedFolders.value.clear()
  updateParentComponent()

  // 清空时重置上传策略
  emit('upload-strategy', { type: 'single', folders: [], singleFiles: [], description: '清空文件' })
}

// 处理错误信息
const handleErrors = (errors: string[]) => {
  if (errors.length > 0) {
    errors.forEach(error => {
      // 如果是文件数量限制的友好提示，显示为信息而不是错误
      if (error.includes('已自动选择前') || error.includes('只能再添加')) {
        toast.info(error)
      } else {
        toast.error(error)
      }
    })
    emit('error', errors)
  }
}

// 更新父组件
const updateParentComponent = () => {
  const fileList = files.value.map(f => f.file)
  emit('update:modelValue', fileList)
  emit('files-change', fileList)
}
</script>
