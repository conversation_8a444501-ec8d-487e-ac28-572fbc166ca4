// 测试文件夹 API 的脚本
// 在 Electron 渲染进程的开发者控制台中运行

async function testFolderAPI() {
  console.log("🧪 开始测试文件夹 API...");

  // 检查 Electron API 是否可用
  console.log("📋 检查 Electron API 可用性:");
  console.log("window.electronAPI:", !!window.electronAPI);
  console.log("window.electronAPI.getFolderFiles:", !!window.electronAPI?.getFolderFiles);
  console.log("window.electronAPI.getFileInfo:", !!window.electronAPI?.getFileInfo);
  console.log("window.electronAPI.showOpenDialog:", !!window.electronAPI?.showOpenDialog);

  if (!window.electronAPI) {
    console.error("❌ Electron API 不可用");
    console.log("💡 请确保应用在 Electron 环境中运行");
    return;
  }

  // 显示所有可用的 API 方法
  console.log("📋 所有可用的 API 方法:", Object.keys(window.electronAPI));

  if (!window.electronAPI.getFolderFiles) {
    console.error("❌ getFolderFiles API 不可用");
    console.log("💡 可能的原因:");
    console.log("   1. preload 脚本没有正确暴露该方法");
    console.log("   2. Electron 应用需要重启以加载新的 preload 脚本");
    console.log("   3. IPC 处理器没有正确注册");
    return;
  }

  // 测试文件夹选择
  console.log("\n📂 测试文件夹选择...");
  try {
    const result = await window.electronAPI.showOpenDialog({
      properties: ["openDirectory"],
      title: "选择测试文件夹",
    });

    if (result.canceled) {
      console.log("ℹ️ 用户取消了文件夹选择");
      return;
    }

    if (!result.filePaths || result.filePaths.length === 0) {
      console.log("ℹ️ 没有选择文件夹");
      return;
    }

    const folderPath = result.filePaths[0];
    console.log("✅ 选择的文件夹:", folderPath);

    // 测试获取文件夹内容
    console.log("\n📄 测试获取文件夹内容...");
    try {
      const folderFiles = await window.electronAPI.getFolderFiles(folderPath);
      console.log("✅ 获取文件夹内容成功");
      console.log(`📊 文件数量: ${folderFiles.length}`);

      if (folderFiles.length > 0) {
        console.log("📋 前5个文件:");
        folderFiles.slice(0, 5).forEach((file, index) => {
          console.log(`  ${index + 1}. ${file.name} (${file.relativePath})`);
          console.log(`     路径: ${file.path}`);
          console.log(`     大小: ${file.size} bytes`);
        });
      } else {
        console.log("📭 文件夹为空");
      }
    } catch (error) {
      console.error("❌ 获取文件夹内容失败:", error);
    }
  } catch (error) {
    console.error("❌ 文件夹选择失败:", error);
  }

  console.log("\n✅ 文件夹 API 测试完成");
}

// 直接测试指定文件夹路径的函数
async function testSpecificFolder(folderPath) {
  console.log(`🧪 测试指定文件夹: ${folderPath}`);

  if (!window.electronAPI?.getFolderFiles) {
    console.error("❌ getFolderFiles API 不可用");
    return;
  }

  try {
    console.log("📂 正在获取文件夹内容...");
    const folderFiles = await window.electronAPI.getFolderFiles(folderPath);
    console.log("✅ 获取成功!");
    console.log(`📊 文件数量: ${folderFiles.length}`);

    if (folderFiles.length > 0) {
      console.log("📋 文件列表:");
      folderFiles.forEach((file, index) => {
        console.log(`  ${index + 1}. ${file.name}`);
        console.log(`     相对路径: ${file.relativePath}`);
        console.log(`     完整路径: ${file.path}`);
        console.log(`     大小: ${file.size} bytes`);
        console.log("");
      });
    } else {
      console.log("📭 文件夹为空");
    }

    return folderFiles;
  } catch (error) {
    console.error("❌ 获取文件夹内容失败:", error);
    throw error;
  }
}

// 快速测试函数 - 测试用户报告的路径
async function quickTest() {
  console.log("🚀 快速测试用户报告的路径...");
  const testPath = "/Users/<USER>/Downloads/安装包";

  try {
    const result = await testSpecificFolder(testPath);
    console.log("✅ 快速测试成功!");
    return result;
  } catch (error) {
    console.error("❌ 快速测试失败:", error);
  }
}

// 运行测试
console.log("选择要运行的测试:");
console.log("1. testFolderAPI() - 完整的文件夹 API 测试");
console.log("2. quickTest() - 快速测试用户报告的路径");
console.log('3. testSpecificFolder("/path/to/folder") - 测试指定路径');

// 默认运行快速测试
quickTest();
